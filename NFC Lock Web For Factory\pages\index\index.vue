<template>
  <view class="page">
    <view :style="{ height: `${statusBarHeight}px` }" />
    <view class="title">
      工单列表
    </view>
    
    <!-- 工单列表 -->
    <view class="work-order-list">
      <view 
        v-for="item in workOrderList" 
        :key="item.id"
        class="work-order-item"
        @click="goToDetail(item)"
      >
        <view class="order-header">
          <view class="order-number">
            {{ item.orderNumber }}
          </view>
          <view class="order-status" :class="item.status === 'completed' ? 'completed' : 'pending'">
            {{ item.status === 'completed' ? '已完成' : '未完成' }}
          </view>
        </view>
        <view class="order-info">
          <view class="info-item">
            <text class="label">
              创建日期：
            </text>
            <text class="value">
              {{ item.createDate }}
            </text>
          </view>
          <view class="info-item">
            <text class="label">
              设备数量：
            </text>
            <text class="value">
              {{ item.deviceCount }} 台
            </text>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 新建工单按钮 -->
    <view class="create-button" @click="createWorkOrder">
      <text class="button-text">
        新建工单
      </text>
    </view>
  </view>
</template>

<script setup>
import { ref, reactive } from "vue";
import { onLoad, onShow } from "@dcloudio/uni-app";

const statusBarHeight = ref((window.android?.getStatusBarHeight?.() || 0) / window.devicePixelRatio);

// 测试数据
const workOrderList = ref([
  {
    id: 1,
    orderNumber: "WO202412001",
    createDate: "2024-12-01",
    deviceCount: 15,
    status: "completed",
  },
  {
    id: 2,
    orderNumber: "WO202412002",
    createDate: "2024-12-02",
    deviceCount: 8,
    status: "pending",
  },
  {
    id: 3,
    orderNumber: "WO202412003",
    createDate: "2024-12-03",
    deviceCount: 22,
    status: "completed",
  },
  {
    id: 4,
    orderNumber: "WO202412004",
    createDate: "2024-12-04",
    deviceCount: 12,
    status: "pending",
  },
  {
    id: 5,
    orderNumber: "WO202412005",
    createDate: "2024-12-05",
    deviceCount: 6,
    status: "pending",
  },
  {
    id: 6,
    orderNumber: "WO202412006",
    createDate: "2024-12-06",
    deviceCount: 18,
    status: "completed",
  },
]);

// 跳转到详情页
const goToDetail = (item) => {
  uni.navigateTo({
    url: `/pages/detail/index?orderId=${item.id}`,
  });
};

// 新建工单
const createWorkOrder = () => {
  uni.navigateTo({
    url: "/pages/detail/index?mode=create",
  });
};

onLoad(() => {
  // 页面加载时的逻辑
});

onShow(() => {
  // 页面显示时的逻辑，可以在这里刷新数据
});
</script>

<style lang="less" scoped>
  @import "./index.less";
</style>
