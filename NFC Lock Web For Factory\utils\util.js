import { i18n } from "@/locales/_i18n.js";

/**
 * 比较两个版本号，判断是否需要更新
 * @param {string} currentVersion - 当前版本号 (如: "1.0.0")
 * @param {string} remoteVersion - 远程版本号 (如: "1.0.1")
 * @returns {boolean} - 如果需要更新返回 true，否则返回 false
 */
export const versionCompare = (currentVersion, remoteVersion) => {
  const current = currentVersion.split(".").map(Number);
  const remote = remoteVersion.split(".").map(Number);
  const maxLength = Math.max(current.length, remote.length);
  while (current.length < maxLength) current.push(0);
  while (remote.length < maxLength) remote.push(0);
  for (let i = 0; i < maxLength; i++) {
    if (remote[i] > current[i]) {
      return true;
    }
    if (remote[i] < current[i]) {
      return false;
    }
  }
  return false;
};

export const uniModal = (content, showCancel = false, callback = () => {}) => {
  uni.showModal({
    title: i18n.global.tm("modalTitleAlert"),
    content,
    confirmText: i18n.global.tm("modalBtnConfirm"),
    cancelText: i18n.global.tm("modalBtnCancel"),
    showCancel,
    success: callback,
  });
};