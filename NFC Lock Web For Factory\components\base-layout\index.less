.page-container {
  position: relative;
  z-index: 1;
  height: 100%;
  display: flex;
  flex-direction: column;

  .page-header {
    position: relative;
    z-index: 9;
    height: 90rpx;
    color: #333;
    font-size: 110%;
    font-weight: bold;
    display: flex;
    justify-content: center;
    align-items: center;
    
    .icon-back {
      position: absolute;
      left: 20rpx;
    }
  }

  .page-content {
    flex: 1;
    overflow-y: auto;
    padding: 0px 32rpx;
  }
  
  .page-footer {
    position: relative;
    z-index: 9;
    height: 110rpx;
    color: #333;
    background-color: white;
    box-shadow: 0rpx -4rpx 20rpx 0rpx rgba(0,0,0,0.02);
    display: flex;

    
    &[disabled="true"] {
      opacity: 0.6;
      pointer-events: none;
    }

    .tab-item {
      position: relative;
      flex: 1;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      cursor: pointer;

      .tab-icon {
        width: 50rpx;
        height: 50rpx;
      }
  
      .tab-text {
        font-size: 20rpx;
      }

      &.active {
        color: #658cff;
      }
    }
  }
}
