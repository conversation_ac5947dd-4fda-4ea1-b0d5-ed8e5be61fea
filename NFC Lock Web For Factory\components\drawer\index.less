.drawer-mask {
  position: fixed;
  z-index: 99;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.4);
}

.drawer-container {
  position: fixed;
  z-index: 100;
  bottom: -9999rpx;
  left: 0rpx;
  padding: 0rpx 40rpx;
  padding-bottom: 30rpx;
  width: 100%;
  background-color: white;
  border-top-left-radius: 30rpx;
  border-top-right-radius: 30rpx;
  box-shadow: 0rpx -2rpx 8rpx rgba(0, 0, 0, 0.25);
  transition: all 0.5s;
  display: flex;
  flex-direction: column;
  
  .drawer-title {
    position: relative;
    padding: 30rpx 0rpx;
    font-size: 36rpx;
    font-weight: bold;
    text-align: center;
    
    .close {
      position: absolute;
      top: 0;
      right: 0;
      height: 100%;
      display: flex;
      align-items: center;
    }
  }
  
  .drawer-content {
    flex: 1;
    height: 0;
  }
}