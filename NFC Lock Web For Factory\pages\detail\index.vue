<template>
  <view class="page">
    <view :style="{ height: `${statusBarHeight}px` }" />
    <view class="title">
      NFC 无源锁工单管理
    </view>
    <!-- 显示检测到的设备 ID -->
    <view class="device-id-list">
      <view v-for="item in deviceIdList" :key="item">
        {{ item }}
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, reactive, watch } from "vue";
import { onLoad, onShow, onHide, onUnload } from "@dcloudio/uni-app";

const statusBarHeight = ref((window.android?.getStatusBarHeight?.() || 0) / window.devicePixelRatio);
const deviceIdList = ref([]);

const onCopy = () => {
  window.android?.copy(textareaContent.value);
};

const setCallbacks = () => {
  window.actionCallback = (name, v1) => {
    if (name === "start") {
      // actionType.value = "";
    } else if (name === "detect") {
      if (v1) {
        deviceIdList.value.push(v1);
      }
    }
  };
};

const clearCallbacks = () => {
  window.actionCallback = () => {};
};

onLoad(() => {
     
});

onShow(() => {
  setCallbacks();
});

onHide(() => {
  clearCallbacks();
});

onUnload(() => {
  clearCallbacks();
});
</script>

<style lang="less" scoped>
  @import "./index.less";
</style>