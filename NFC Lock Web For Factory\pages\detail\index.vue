<template>
  <view class="page">
    <view :style="{ height: `${statusBarHeight}px` }" />
    <view class="title">
      <uv-icon name="arrow-left" size="22" @click="onBack" />
      {{ pageTitle }}
    </view>

    <!-- 工单信息 -->
    <view v-if="orderInfo.orderNumber" class="order-info">
      <view class="info-item">
        <text class="label">
          工单编号：
        </text>
        <text class="value">
          {{ orderInfo.orderNumber }}
        </text>
      </view>
      <view class="info-item">
        <text class="label">
          创建日期：
        </text>
        <text class="value">
          {{ orderInfo.createDate }}
        </text>
      </view>
      <view class="info-item">
        <text class="label">
          工单状态：
        </text>
        <text class="value status" :class="orderInfo.status === 'completed' ? 'completed' : 'pending'">
          {{ orderInfo.status === 'completed' ? '已完成' : '未完成' }}
        </text>
      </view>
    </view>

    <!-- 设备 ID 列表 -->
    <view class="device-section">
      <view class="section-title">
        检测到的设备 ID ({{ deviceIdList.length }})
      </view>
      <view class="device-id-list">
        <view v-if="deviceIdList.length === 0" class="empty-state">
          暂无检测到设备 ID
        </view>
        <view v-else>
          <view
            v-for="(item, index) in deviceIdList"
            :id="`device-item-${item}`"
            :key="item"
            class="device-item"
            :class="getDeviceItemClass(item)"
          >
            <text class="device-id">
              {{ item }}
            </text>
            <text class="device-index">
              {{ index + 1 }}
            </text>
          </view>
        </view>
      </view>
    </view>

    <!-- 操作按钮 -->
    <view class="button-group">
      <template v-if="isCompleted">
        <!-- 已完成工单的按钮 -->
        <view class="button secondary" @click="copyDeviceIds">
          复制设备 ID
        </view>
        <view class="button danger" @click="deleteOrder">
          删除工单
        </view>
      </template>
      <template v-else>
        <!-- 未完成工单的按钮 -->
        <view class="button secondary" @click="clearDeviceList">
          清空列表
        </view>
        <view class="button secondary" @click="copyDeviceIds">
          复制设备 ID
        </view>
        <view class="button primary" @click="completeOrder">
          完成工单
        </view>
      </template>
    </view>
  </view>
</template>

<script setup>
import { ref, computed, nextTick } from "vue";
import { onLoad, onShow, onHide, onUnload } from "@dcloudio/uni-app";

const statusBarHeight = ref((window.android?.getStatusBarHeight?.() || 0) / window.devicePixelRatio);
const deviceIdList = ref([]);
const orderInfo = ref({});
const pageMode = ref(""); // 'create', 'view', 'edit'
const orderId = ref("");

// 设备高亮状态管理
const deviceHighlightState = ref({}); // { deviceId: 'new' | 'duplicate' | null }

// 计算属性
const pageTitle = computed(() => {
  if (pageMode.value === "create") return "新建工单";
  return "工单详情";
});

const isCompleted = computed(() => {
  return orderInfo.value.status === "completed";
});

// 返回按钮功能
const onBack = () => {
  uni.navigateBack();
};

// 获取设备项的样式类
const getDeviceItemClass = (deviceId) => {
  const state = deviceHighlightState.value[deviceId];
  if (state === "new") return "highlight-new";
  if (state === "duplicate") return "highlight-duplicate";
  return "";
};

// 清除设备高亮状态
const clearDeviceHighlight = (deviceId) => {
  setTimeout(() => {
    deviceHighlightState.value[deviceId] = null;
  }, 2000); // 2秒后清除高亮
};

// 滚动到指定设备
const scrollToDevice = (deviceId) => {
  // 使用 nextTick 确保 DOM 已更新
  nextTick(() => {
    // 使用 scrollIntoView API 滚动到指定元素
    uni.createSelectorQuery()
      .select(`#device-item-${deviceId}`)
      .boundingClientRect((rect) => {
        if (rect) {
          // 获取设备列表容器信息
          uni.createSelectorQuery()
            .select(".device-id-list")
            .boundingClientRect((listRect) => {
              uni.createSelectorQuery()
                .select(".device-id-list")
                .scrollOffset((scroll) => {
                  if (listRect && scroll) {
                    // 计算元素相对于容器的位置
                    const elementTop = rect.top - listRect.top + scroll.scrollTop;
                    // 计算滚动位置，让元素显示在容器中央
                    const scrollTop = elementTop - (listRect.height / 2) + (rect.height / 2);

                    // 执行滚动动画
                    uni.pageScrollTo({
                      scrollTop: Math.max(0, scrollTop),
                      duration: 300,
                      selector: ".device-id-list",
                    });
                  }
                }).exec();
            }).exec();
        }
      }).exec();
  });
};

// 模拟工单详情数据 - 与列表页面对应的测试数据
const mockOrderData = {
  1: {
    id: 1,
    orderNumber: "WO202412001",
    createDate: "2024-12-01",
    status: "completed",
    deviceIds: ["NFC001", "NFC002", "NFC003", "NFC004", "NFC005", "NFC006", "NFC007", "NFC008", "NFC009", "NFC010", "NFC011", "NFC012", "NFC013", "NFC014", "NFC015", "NFC016", "NFC017", "NFC018", "NFC019", "NFC020"],
  },
  2: {
    id: 2,
    orderNumber: "WO202412002",
    createDate: "2024-12-02",
    status: "pending",
    deviceIds: ["NFC001", "NFC002", "NFC003", "NFC004", "NFC005", "NFC006", "NFC007", "NFC008", "NFC009", "NFC010", "NFC011", "NFC012", "NFC013", "NFC014", "NFC015", "NFC016", "NFC017", "NFC018", "NFC019", "NFC020"],
  },
};

// 复制设备 ID 到剪贴板
const copyDeviceIds = () => {
  if (deviceIdList.value.length === 0) {
    uni.showToast({
      title: "暂无设备 ID",
      icon: "none",
    });
    return;
  }

  const idsText = deviceIdList.value.join("\n");
  window.android?.copy(idsText);
  uni.showToast({
    title: "已复制到剪贴板",
    icon: "success",
  });
};

// 清空设备列表
const clearDeviceList = () => {
  uni.showModal({
    title: "确认清空",
    content: "确定要清空所有检测到的设备 ID 吗？",
    success: (res) => {
      if (res.confirm) {
        deviceIdList.value = [];
        deviceHighlightState.value = {}; // 清除所有高亮状态
        uni.showToast({
          title: "已清空列表",
          icon: "success",
        });
      }
    },
  });
};

// 完成工单
const completeOrder = () => {
  if (deviceIdList.value.length === 0) {
    uni.showToast({
      title: "请先检测设备",
      icon: "none",
    });
    return;
  }

  uni.showModal({
    title: "确认完成",
    content: `确定要完成工单吗？共检测到 ${deviceIdList.value.length} 个设备。`,
    success: (res) => {
      if (res.confirm) {
        // TODO: 调用接口提交工单数据
        submitWorkOrder();
      }
    },
  });
};

// 提交工单数据
const submitWorkOrder = async () => {
  try {
    // TODO: 实际的接口调用
    // const result = await api.submitWorkOrder({
    //   orderId: orderId.value,
    //   deviceIds: deviceIdList.value
    // });

    // 模拟接口调用
    await new Promise(resolve => setTimeout(resolve, 1000));

    uni.showToast({
      title: "工单已完成",
      icon: "success",
    });

    // 返回列表页面
    setTimeout(() => {
      uni.navigateBack();
    }, 1500);
  } catch (error) {
    uni.showToast({
      title: "提交失败，请重试",
      icon: "none",
    });
  }
};

// 删除工单
const deleteOrder = () => {
  uni.showModal({
    title: "确认删除",
    content: "确定要删除这个工单吗？删除后无法恢复。",
    success: (res) => {
      if (res.confirm) {
        // TODO: 调用接口删除工单
        deleteWorkOrder();
      }
    },
  });
};

// 删除工单数据
const deleteWorkOrder = async () => {
  try {
    // TODO: 实际的接口调用
    // const result = await api.deleteWorkOrder(orderId.value);

    // 模拟接口调用
    await new Promise(resolve => setTimeout(resolve, 1000));

    uni.showToast({
      title: "工单已删除",
      icon: "success",
    });

    // 返回列表页面
    setTimeout(() => {
      uni.navigateBack();
    }, 1500);
  } catch (error) {
    uni.showToast({
      title: "删除失败，请重试",
      icon: "none",
    });
  }
};

// 加载工单详情
const loadOrderDetail = async (id) => {
  try {
    // TODO: 实际的接口调用
    // const result = await api.getWorkOrderDetail(id);
    // orderInfo.value = result.data;
    // deviceIdList.value = result.data.deviceIds || [];

    // 使用模拟数据
    const mockData = mockOrderData[id];
    if (mockData) {
      orderInfo.value = mockData;
      deviceIdList.value = [...mockData.deviceIds];
    }
  } catch (error) {
    uni.showToast({
      title: "加载失败，请重试",
      icon: "none",
    });
  }
};

// 设置 NFC 检测回调
const setCallbacks = () => {
  window.actionCallback = (name, v1) => {
    if (name === "detect" && v1) {
      // 检查是否为已完成工单，如果是则忽略
      if (isCompleted.value) {
        return;
      }

      // 去重处理
      if (!deviceIdList.value.includes(v1)) {
        // 新设备：添加到列表最上方并高亮为主题色
        deviceIdList.value.unshift(v1);
        deviceHighlightState.value[v1] = "new";
        clearDeviceHighlight(v1);
        // 滚动到新设备位置（新设备在最上方，所以滚动到顶部）
        setTimeout(() => {
          scrollToDevice(v1);
        }, 100);
      } else {
        // 重复设备：高亮为警告色
        deviceHighlightState.value[v1] = "duplicate";
        clearDeviceHighlight(v1);
        // 滚动到重复设备位置
        setTimeout(() => {
          scrollToDevice(v1);
        }, 100);
      }
    }
  };
};

const clearCallbacks = () => {
  window.actionCallback = () => {};
};

onLoad((options) => {
  if (options.orderId) {
    // 查看已有工单
    orderId.value = options.orderId;
    pageMode.value = "view";
    loadOrderDetail(options.orderId);
  } else if (options.mode === "create") {
    // 新建工单
    pageMode.value = "create";
    const now = new Date();
    const year = now.getFullYear();
    const month = String(now.getMonth() + 1).padStart(2, "0");
    const day = String(now.getDate()).padStart(2, "0");
    const random = String(Math.floor(Math.random() * 1000)).padStart(3, "0");

    orderInfo.value = {
      orderNumber: `WO${year}${month}${day}${random}`,
      createDate: now.toISOString().split("T")[0],
      status: "pending",
    };
  }
});

onShow(() => {
  setCallbacks();
});

onHide(() => {
  clearCallbacks();
});

onUnload(() => {
  clearCallbacks();
});
</script>

<style lang="less" scoped>
  @import "./index.less";
</style>