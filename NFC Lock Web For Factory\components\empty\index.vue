<template>
  <view class="empty-box">
    <uv-empty
      :mode="mode"
      :icon-size="140"
      :text-size="16"
      :text="textI18N"
    />
    <view
      v-if="button"
      class="btns"
    >
      <view @click="onButtonClick">
        {{ button }}
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref } from "vue";
import { useI18n } from "vue-i18n";

const props = defineProps({
  mode: {
    type: String,
    default: "data",
  },
  text: {
    type: String,
    default: "",
  },
  button: {
    type: String,
    default: "",
  },
});

const emits = defineEmits(["buttonClick"]);

const { tm: i18n } = useI18n();
const textI18N = ref(props.text || i18n("defaultEmpty"));

const onButtonClick = () => {
  emits("buttonClick");
};
</script>

<style lang="less" scoped>
  @import "./index.less";
</style>