<template>
  <view class="page-container">
    <view v-if="pageTitle" class="page-header">
      <view v-if="showBack" class="icon-back" @click="onBack">
        <uv-icon name="arrow-left" size="22" />
      </view>
      <text>{{ pageTitle }}</text>
    </view>
    <view class="page-content">
      <slot name="content" />
    </view>
    <view v-if="showTab" class="page-footer" :disabled="!tabEnable">
      <view v-for="(item, index) in tabBarItemsI18N" :key="index" class="tab-item" :class="{ active: selectedIndex === item.index }" @click="onTabClick(item)">
        <uv-badge v-if="(index === tabBarItemsI18N.length - 1) && versionInfo.url" :absolute="true" :offset="[8, 30]" :is-dot="true" type="error" />
        <uv-icon :name="item.index === selectedIndex ? item.iconSelected : item.icon" :color="item.index === selectedIndex ? '#658cff' : '#555555'" :size="item.size || 28" />
        <text class="tab-text">
          {{ item.name }}
        </text>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, watch } from "vue";
import { useI18n } from "vue-i18n";
import { versionCompare } from "@/utils/util.js";
import { UserInfoApi, VersionApi } from "@/utils/api.js";
import { tabBarItems, roleInfo } from "./config.js";

const props = defineProps({
  pageTitle: {
    type: String,
    default: "",
  },
  showBack: {
    type: Boolean,
    default: false,
  },
  selectedIndex: {
    type: Number,
    default: -1,
  },
  showTab: {
    type: Boolean,
    default: true,
  },
  tabEnable: {
    type: Boolean,
    default: true,
  },
  reloadUserInfo: {
    type: Boolean,
    default: false,
  },
});

const emits = defineEmits(["userInfo", "versionInfo", "update:reloadUserInfo"]);

const { t: i18n, locale } = useI18n();
const language = uni.getStorageSync("lang");
if (language) {
  locale.value = language;
} else {
  if (navigator.language.startsWith("zh")) {
    locale.value = "zh";
  } else if (navigator.language.startsWith("en")) {
    locale.value = "en";
  } else {
    locale.value = "en";
  }
}
const userInfo = ref({});
const versionInfo = ref({});
const tabBarItemsI18N = ref(tabBarItems(i18n));

const onBack = () => {
  uni.navigateBack({
    delta: 1,
  });
};

const onTabClick = (item) => {
  if (item.path) {
    uni.redirectTo({
      url: item.path,
    });
  }
};

const getUserInfo = async () => {
  const token = uni.getStorageSync("token");
  if (token) {
    const api = new UserInfoApi().token(token);
    await api.send();
    const result = await api.getData();
    const info = {
      ...result.data,
      protectedPhone: result.data?.phone?.replace?.(/(\d{3})\d*(\d{4})/, "$1****$2"),
      token,
    };
    userInfo.value = info;
    emits("userInfo", info);
  } else {
    uni.reLaunch({
      url: "/pages/login/index",
    });
  }
};

const getVersionList = async () => {
  const api = new VersionApi().token(userInfo.value.token).data({});
  await api.send();
  const result = await api.getData();
  return result.data;
};

const getVersionInfo = async () => {
  const info = {
    version: window.android?.getVersion?.(),
  };
  const list = await getVersionList();
  if (info.version && list.apk_version) {
    const isAdmin = userInfo.value.role_id === roleInfo.ADMIN;
    info.test = !(list.apk_version.split(".").slice(-1)[0] % 2);
    if (versionCompare(info.version?.split?.(" ")[0], list.apk_version)) {
      if (!info.test || (info.test && isAdmin)) {
        info.url = `${VersionApi.baseUrl}/${list.apk_url}`;
      }
    }
  }
  versionInfo.value = info;
  emits("versionInfo", info);
};

getUserInfo().then(() => {
  getVersionInfo();
});

watch(() => props.reloadUserInfo, () => {
  if (props.reloadUserInfo) {
    getUserInfo();
    emits("update:reloadUserInfo", false);
  }
});
</script>

<style lang="less" scoped>
  @import "./index.less";
</style>