<template>
  <view class="list-container">
    <scroll-view
      class="list-items"
      scroll-y
      @scrolltolower="onScrollToLower"
    >
      <template v-if="datas.length">
        <checkbox-group @change="onCheckboxChange">
          <view class="flow-root">
            <view
              v-for="(listItem, listIndex) in datas"
              :key="listIndex"
              :class="`list-item ${listType}`"
            >
              <view class="info-box">
                <view class="title">
                  {{ listItem.title }}
                </view>
                <view class="title-sub">
                  {{ listItem.titleSub }}
                </view>
              </view>
              <view class="btn-box">
                <view
                  v-if="hasShare"
                  class="share"
                  @click="onShare(listItem)"
                >
                  {{ hasShare }}
                </view>
                <view
                  v-if="hasRecord"
                  class="record"
                  @click="toRecord(listItem)"
                >
                  {{ hasRecord }}
                </view>
                <view
                  v-if="hasEdit"
                  class="edit"
                  @click="onEdit(listItem)"
                >
                  {{ hasEdit }}
                </view>
                <view
                  v-if="hasDelete"
                  class="delete"
                  @click="onDelete(listItem)"
                >
                  {{ hasDelete }}
                </view>
              </view>
              <view
                v-if="hasSelection"
                class="selection"
              >
                <checkbox
                  :value="String(listItem.id)"
                  :checked="selectedItem.includes(listItem.id)"
                />
              </view>
            </view>
          </view>
        </checkbox-group>
      </template>
      <view
        v-else-if="empty"
        class="empty"
      >
        <empty
          mode="list"
          :text="empty"
        />
      </view>
    </scroll-view>
    <view
      v-if="datas.length && hasPagination"
      class="pagination"
    >
      <uni-pagination
        :current="currentPage"
        :page-size="pageSize"
        :total="total"
        :prev-text="i18n('paginationPrevText')"
        :next-text="i18n('paginationNextText')"
        @change="onPageChange"
      />
    </view>
    <view
      v-if="hasSelection"
      class="selection-btns"
    >
      <view
        v-if="datas.length"
        @click="onConfirm"
      >
        {{ i18n("btnConfirm") }}
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, watch } from "vue";
import { useI18n } from "vue-i18n";

import Empty from "../empty/index.vue";

const props = defineProps({
  listType: {
    type: String,
    default: "list",
  },
  datas: {
    type: Array,
    default: () => [],
  },
  pageSize: {
    type: Number,
    default: 10,
  },
  total: {
    type: Number,
    default: 0,
  },
  alert: {
    type: String,
    default: "",
  },
  hasShare: {
    default: false,
  },
  hasRecord: {
    default: false,
  },
  hasEdit: {
    default: false,
  },
  hasDelete: {
    default: false,
  },
  hasPagination: {
    type: Boolean,
    default: true,
  },
  hasSelection: {
    type: Boolean,
    default: false,
  },
  empty: {
    type: String,
    default: "",
  },
});

const emits = defineEmits(["select", "share", "record", "edit", "delete", "pageChange", "scrolltolower"]);

const { tm: i18n } = useI18n();
const selectedItem = ref([]);
const currentPage = ref(1);

const onCheckboxChange = (event) => {
  selectedItem.value = event.detail.value;
};

const selectAll = () => {
  const allSelection = props.datas.map((item) => item.id);
  selectedItem.value = allSelection;
};

const selectReverse = () => {
  const allSelection = props.datas.map((item) => item.id);
  const reverseSelection = allSelection.filter((item) => !selectedItem.value.includes(item));
  selectedItem.value = reverseSelection;
};

const isAllSelected = () => {
  return selectedItem.value.length === props.datas.length;
};

const onShare = (item) => {
  emits("share", item);
};

const toRecord = (item) => {
  emits("record", item);
};

const onEdit = (item) => {
  emits("edit", item);
};

const onDelete = (item) => {
  emits("delete", item);
};

const setPage = (page) => {
  currentPage.value = page;
};

const onPageChange = ({ current }) => {
  currentPage.value = current;
  emits("pageChange", current);
  // 取消所有选择
  selectAll();
  selectReverse();
};

const onScrollToLower = () => {
  if (currentPage.value < Math.ceil(props.total / props.pageSize)) {
    emits("scrolltolower");
  }
};

const onConfirm = () => {
  if (selectedItem.value.length) {
    if (props.alert) {
      uni.showModal({
        title: i18n("modalTitleAlert"),
        content: props.alert,
        confirmText: i18n("modalBtnConfirm"),
        cancelText: i18n("modalBtnCancel"),
        success: (result) => {
          if (result.confirm) {
            emits("select", selectedItem.value);
          }
        },
      });
    } else {
      emits("select", selectedItem.value);
    }
  } else {
    uni.showModal({
      title: i18n("modalTitleAlert"),
      content: i18n("modalContentNoItemSelected"),
      confirmText: i18n("modalBtnConfirm"),
      showCancel: false,
    });
  }
};

watch(() => props.hasSelection, () => {
  selectedItem.value = [];
});

defineExpose({
  selectAll,
  selectReverse,
  isAllSelected,
  setPage,
});
</script>

<style lang="less" scoped>
  @import "./index.less";
</style>