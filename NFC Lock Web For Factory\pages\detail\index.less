.page {
  width: 100%;
  height: 100vh;
  background-color: #f5f5f5;
  display: flex;
  flex-direction: column;

  .title {
    font-weight: bold;
    text-align: center;
    font-size: 36rpx;
    color: #333333;
    margin-bottom: 40rpx;
    background-color: white;
    padding: 30rpx 40rpx;
    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;

    // 返回按钮样式
    :deep(.uv-icon) {
      position: absolute;
      left: 10rpx;
      top: 50%;
      transform: translateY(-50%);
      color: #333333;
      cursor: pointer;
      padding: 10rpx;

      &:active {
        opacity: 0.6;
      }
    }
  }

  .order-info {
    background-color: white;
    margin: 0 30rpx 20rpx 30rpx;
    padding: 30rpx;
    border-radius: 16rpx;
    box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);

    .info-item {
      display: flex;
      align-items: center;
      margin-bottom: 20rpx;

      &:last-child {
        margin-bottom: 0;
      }

      .label {
        font-size: 28rpx;
        color: #666666;
        min-width: 140rpx;
      }

      .value {
        font-size: 28rpx;
        color: #333333;
        font-weight: 500;

        &.status {
          padding: 6rpx 12rpx;
          border-radius: 16rpx;
          font-size: 24rpx;

          &.completed {
            background-color: #e8f5e8;
            color: #52c41a;
          }

          &.pending {
            background-color: #fff7e6;
            color: #fa8c16;
          }
        }
      }
    }
  }

  .device-section {
    flex: 1;
    height: 0;
    margin: 0 30rpx;
    display: flex;
    flex-direction: column;

    .section-title {
      font-size: 32rpx;
      font-weight: 600;
      color: #333333;
      margin-bottom: 20rpx;
    }

    .device-id-list {
      flex: 1;
      height: 0;
      background-color: white;
      border-radius: 16rpx;
      padding: 30rpx;
      box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
      overflow-y: auto;

      .empty-state {
        text-align: center;
        color: #999999;
        font-size: 28rpx;
        padding: 60rpx 0;
      }

      .device-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 20rpx 0;
        border-bottom: 1rpx solid #f0f0f0;
        transition: all 0.3s ease;

        &:last-child {
          border-bottom: none;
        }

        // 新设备高亮样式（主题色）
        &.highlight-new {
          background-color: #e6f7ff;
          border-radius: 8rpx;
          padding: 20rpx 16rpx;
          margin: 0 -16rpx;
          border-bottom: 1rpx solid #91d5ff;
        }

        // 重复设备高亮样式（警告色）
        &.highlight-duplicate {
          background-color: #fff7e6;
          border-radius: 8rpx;
          padding: 20rpx 16rpx;
          margin: 0 -16rpx;
          border-bottom: 1rpx solid #ffd591;
        }

        .device-id {
          font-size: 30rpx;
          color: #333333;
          font-family: 'Courier New', monospace;
        }

        .device-index {
          font-size: 24rpx;
          color: #999999;
          background-color: #f5f5f5;
          padding: 4rpx 12rpx;
          border-radius: 12rpx;
        }

        // 高亮状态下的设备索引样式
        &.highlight-new .device-index {
          background-color: #1890ff;
          color: white;
        }

        &.highlight-duplicate .device-index {
          background-color: #fa8c16;
          color: white;
        }
      }
    }
  }

  .button-group {
    padding: 30rpx;
    display: flex;
    gap: 20rpx;

    .button {
      flex: 1;
      height: 88rpx;
      border-radius: 44rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 32rpx;
      font-weight: 600;
      transition: all 0.3s ease;

      &:active {
        transform: scale(0.95);
      }

      &.primary {
        background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%);
        color: white;
        box-shadow: 0 8rpx 24rpx rgba(24, 144, 255, 0.3);

        &:active {
          box-shadow: 0 4rpx 16rpx rgba(24, 144, 255, 0.4);
        }
      }

      &.secondary {
        background-color: #f5f5f5;
        color: #666666;
        border: 1rpx solid #d9d9d9;

        &:active {
          background-color: #e6e6e6;
        }
      }

      &.danger {
        background-color: #ff4d4f;
        color: white;
        box-shadow: 0 8rpx 24rpx rgba(255, 77, 79, 0.3);

        &:active {
          box-shadow: 0 4rpx 16rpx rgba(255, 77, 79, 0.4);
        }
      }
    }
  }
}

// 设备列表滚动条样式
.device-id-list::-webkit-scrollbar {
  width: 8rpx;
}

.device-id-list::-webkit-scrollbar-track {
  background: #f5f5f5;
  border-radius: 4rpx;
}

.device-id-list::-webkit-scrollbar-thumb {
  background: #d9d9d9;
  border-radius: 4rpx;
}

.device-id-list::-webkit-scrollbar-thumb:hover {
  background: #bfbfbf;
}