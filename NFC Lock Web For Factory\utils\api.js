import { Request } from "./request.js";
import { i18n } from "@/locales/_i18n.js";

Request.baseUrl = "http://47.102.109.244:8787";
// Request.baseUrl = "http://192.168.100.21:3000/proxy";

// 提交 Modal 提示
class RequestWithModal extends Request {
  async send(content) {
    if (content) {
      await new Promise((resolve) => {
        uni.showModal({
          title: i18n.global.tm("modalTitleAlert"),
          content,
          confirmText: i18n.global.tm("modalBtnConfirm"),
          cancelText: i18n.global.tm("modalBtnCancel"),
          success: ({ confirm }) => {
            if (confirm) {
              super.send();
              resolve();
            }
          },
          fail: () => {
            resolve();
          },
        });
      });
    } else {
      super.send();
    }
  }

  async getData(successHint, failHint, showModalOnError = false) {
    try {
      const data = await super.getData();
      const code = data.code;
      if (successHint && failHint) {
        await new Promise((resolve) => {
          uni.showModal({
            title: i18n.global.tm("modalTitleAlert"),
            content: code === 200 ? successHint : failHint,
            confirmText: i18n.global.tm("modalBtnConfirm"),
            showCancel: false,
            complete() {
              resolve();
            },
          });
        });
      }
      return data;
    } catch (err) {
      if (showModalOnError) {
        uni.showModal({
          title: i18n.global.tm("modalTitleAlert"),
          content: err.message,
          confirmText: i18n.global.tm("modalBtnConfirm"),
          showCancel: false,
        });
      }
      throw err;
    }
  }
}

// 网络连接测试
export class NetWorkTestApi extends RequestWithModal {
  constructor() {
    super("/user/me");
    this.method(Request.methods.POST);
  }

  async test(token, showModalOnError = false) {
    this.token(token);
    await this.send();
    try {
      await this.getData(null, null, showModalOnError);
      return true;
    } catch (err) {
      return false;
    }
  }
}

// token 有效性判断
class RequestWithTokenValidCheck extends RequestWithModal {
  async getData(successHint, failHint, showModalOnError) {
    const data = await super.getData(successHint, failHint, showModalOnError);
    const code = data.code;
    if (code === -1) {
      uni.showModal({
        title: i18n.global.tm("modalTitleAlert"),
        content: i18n.global.tm("modalContentTokenInvalid"),
        confirmText: i18n.global.tm("modalBtnConfirm"),
        showCancel: false,
        success: () => {
          uni.removeStorageSync("token");
          uni.reLaunch({
            url: "/pages/login/index",
          });
        },
      });
      throw new Error("Invalid Token");
    } else {
      return data;
    }
  }
}
