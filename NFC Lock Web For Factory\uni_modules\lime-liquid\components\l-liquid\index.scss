@import '~@/uni_modules/lime-style/index.scss';
// $border-radius-circle: 50% !default;
// $primary-color: #007aff !default;
// $shadow: create-var(
// 	shadow, 
// 	0 6px 16px 0 rgba(0, 0, 0, 0.08), 
// 	0 3px 6px -4px rgba(0, 0, 0, 0.12),      
// 	0 9px 28px 8px rgba(0, 0, 0, 0.05)
// );
// 有用户在iphone8，iphone13 测试 radius 不生效
// vue2 var 不支持rpx。。。。。。神奇	
$liquid-bg-color: create-var(liquid-bg-color, transprent);
$liquid-size: create-var(liquid-size, 125px);
$liquid-aspect-ratio: create-var(liquid-aspect-ratio, 1);
$liquid-border-radius: create-var(liquid-border-radius, $border-radius-hg);
$liquid-inner-border-radius: create-var(liquid-inner-border-radius, $liquid-border-radius);
$liquid-wave-color: create-var(liquid-wave-color, $primary-color);
$liquid-border-distance: create-var(liquid-border-distance, 6px);
$liquid-border-width: create-var(liquid-border-width, 6px);
$liquid-border-color: create-var(liquid-border-color, $primary-color);

.l-liquid {
	position: relative;
	border-radius: $liquid-border-radius;
	box-sizing: border-box;
	overflow: hidden;
	width: $liquid-size;
	height: $liquid-size;
	// aspect-ratio: $liquid-aspect-ratio;
	background: $liquid-bg-color;
	&--outline {
		padding: $liquid-border-distance;
		border: $liquid-border-width solid $liquid-border-color;
		box-shadow: $shadow;
	}
	&__inner {
		position: relative;
		width: 100%;
		height: 100%;
		border-radius: $liquid-inner-border-radius;
		overflow: hidden;
		-webkit-transform: translate3d(0, 0, 0);
	}
	&__value {
		position: absolute;
		top: 0;
		left: 0;
		width: 100%;
		height: 100%;
		z-index: 1;
		display: flex;
		justify-content: center;
		align-items: center;
	}
	&__waves {
		position: absolute;
		left: 0;
		width: 100%;
		height: 100%;
		transform: translateY(var(--l-liquid-percent));
		transition-property: transform;
		transition-duration: 300ms;
	}
	.wave {
		position: absolute;
		left: 0;
		top: 0;
		height: 200%;
		width: 200%;
		animation: wave 2500ms linear infinite;
		background: $liquid-wave-color;
		opacity: 1;
		clip-path: polygon(0% 50%, 2.22222% 51.472%, 4.44444% 52.4967%, 6.66667% 52.7626%, 8.88889% 52.1889%, 11.1111% 50.9501%, 13.3333% 49.4225%, 15.5556% 48.0704%, 17.7778% 47.3047%, 20% 47.3582%, 22.2222% 48.2145%, 24.4444% 49.6134%, 26.6667% 51.1298%, 28.8889% 52.3029%, 31.1111% 52.7761%, 33.3333% 52.4056%, 35.5556% 51.3041%, 37.7778% 49.8062%, 40% 48.3673%, 42.2222% 47.4245%, 44.4444% 47.2644%, 46.6667% 47.9357%, 48.8889% 49.2343%, 51.1111% 50.7657%, 53.3333% 52.0643%, 55.5556% 52.7356%, 57.7778% 52.5755%, 60% 51.6327%, 62.2222% 50.1938%, 64.4444% 48.6959%, 66.6667% 47.5944%, 68.8889% 47.2239%, 71.1111% 47.6971%, 73.3333% 48.8702%, 75.5556% 50.3866%, 77.7778% 51.7855%, 80% 52.6418%, 82.2222% 52.6953%, 84.4444% 51.9296%, 86.6667% 50.5775%, 88.8889% 49.0499%, 91.1111% 47.8111%, 93.3333% 47.2374%, 95.5556% 47.5033%, 97.7778% 48.528%, 100% 50%, 100% 100%, 0% 100%, 0% 0%);
		&.two {
			opacity: 0.3;
			animation: wave 2500ms linear -612ms infinite;
		}
	}
}


@keyframes wave {
	to {
		transform: translateX(-50%);
	}
}