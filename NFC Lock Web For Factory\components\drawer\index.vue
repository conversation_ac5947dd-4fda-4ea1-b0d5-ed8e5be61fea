<template>
  <view
    v-if="open && showMask"
    class="drawer-mask"
    :style="{ backgroundColor: `rgba(0, 0, 0, ${maskOpacity})` }"
  />
  <view
    class="drawer-container"
    :style="{ height: `${drawerHeight}px`, bottom: `${open ? 0 : -drawerHeight}px`, visibility: open ? 'visible' : 'hidden' }"
  >
    <view class="drawer-title">
      <text>{{ title }}</text>
      <view
        v-if="closable"
        class="close"
        @click="onClose"
      >
        <uv-icon
          name="close"
          size="20"
        />
      </view>
    </view>
    <view
      v-if="open"
      class="drawer-content"
    >
      <slot name="content" />
    </view>
  </view>
</template>

<script setup>
import { computed, ref } from "vue";
import { useI18n } from "vue-i18n";

const props = defineProps({
  open: {
    type: Boolean,
    default: false,
  },
  maskOpacity: {
    type: Number,
    default: 0.4,
  },
  showMask: {
    type: Boolean,
    default: true,
  },
  title: {
    type: String,
    default: "标题",
  },
  height: {
    type: Number,
    default: 800,
  },
  closable: {
    type: Boolean,
    default: true,
  },
});

const emits = defineEmits(["close"]);

const { tm: i18n } = useI18n();
const titleI18N = ref(props.title || i18n("defaultTitle"));

const screenHeight = ref(window.screen.availHeight);

// const systemInfo = uni.getSystemInfoSync();
// screenHeight.value = systemInfo.screenHeight * 750 / systemInfo.screenWidth;

const drawerHeight = computed(() => {
  if (props.height > 0) {
    return uni.upx2px(props.height);
  } else {
    const baseHeight = uni.upx2px(props.height) + screenHeight.value;
    const statusBarHeight = (window.android?.getStatusBarHeight?.() || 0) / window.devicePixelRatio;
    const navigationBarHeight = (window.android?.getNavigationBarHeight?.() || 0) / window.devicePixelRatio;
    return baseHeight - statusBarHeight - navigationBarHeight;
  }
});

const onClose = () => {
  emits("close");
};
</script>

<style lang="less" scoped>
  @import "./index.less";
</style>