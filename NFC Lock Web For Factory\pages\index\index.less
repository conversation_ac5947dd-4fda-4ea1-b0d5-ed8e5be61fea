.page {
  width: 100%;
  height: 100vh;
  background-color: #f5f5f5;
  display: flex;
  flex-direction: column;

  .title {
    font-weight: bold;
    text-align: center;
    font-size: 36rpx;
    color: #333333;
    margin-bottom: 40rpx;
    background-color: white;
    padding: 30rpx 0;
    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  }

  .work-order-list {
    flex: 1;
    padding: 0 30rpx 180rpx 30rpx; // 增加底部内边距，为按钮留出更多空间
    overflow-y: auto;

    .work-order-item {
      background-color: white;
      border-radius: 16rpx;
      padding: 30rpx;
      margin-bottom: 20rpx;
      box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
      transition: all 0.3s ease;

      &:active {
        transform: scale(0.98);
        box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.12);
      }

      .order-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20rpx;

        .order-number {
          font-size: 32rpx;
          font-weight: 600;
          color: #333333;
        }

        .order-status {
          padding: 8rpx 16rpx;
          border-radius: 20rpx;
          font-size: 24rpx;
          font-weight: 500;

          &.completed {
            background-color: #e8f5e8;
            color: #52c41a;
            border: 1rpx solid #b7eb8f;
          }

          &.pending {
            background-color: #fff7e6;
            color: #fa8c16;
            border: 1rpx solid #ffd591;
          }
        }
      }

      .order-info {
        .info-item {
          display: flex;
          align-items: center;
          margin-bottom: 12rpx;

          &:last-child {
            margin-bottom: 0;
          }

          .label {
            font-size: 28rpx;
            color: #666666;
            min-width: 140rpx;
          }

          .value {
            font-size: 28rpx;
            color: #333333;
            font-weight: 500;
          }
        }
      }
    }
  }

  .create-button {
    position: fixed;
    bottom: 60rpx;
    left: 30rpx;
    right: 30rpx;
    height: 88rpx;
    background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%);
    border-radius: 44rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 8rpx 24rpx rgba(24, 144, 255, 0.3);
    transition: all 0.3s ease;

    &:active {
      transform: scale(0.95);
      box-shadow: 0 4rpx 16rpx rgba(24, 144, 255, 0.4);
    }

    .button-text {
      font-size: 32rpx;
      color: white;
      font-weight: 600;
    }
  }
}

// 滚动条样式优化
::-webkit-scrollbar {
  width: 0;
  background: transparent;
}

// 空状态样式（如果需要的话）
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 40rpx;
  color: #999999;

  .empty-icon {
    font-size: 120rpx;
    margin-bottom: 30rpx;
  }

  .empty-text {
    font-size: 28rpx;
  }
}
