// 超出行数，自动显示行尾省略号，最多5行
// 来自uvui的温馨提示：当您在控制台看到此报错，说明需要在App.vue的style标签加上【lang="scss"】
@for $i from 1 through 5 {
	.uv-line-#{$i} {
		/* #ifdef APP-NVUE */
		// nvue下，可以直接使用lines属性，这是weex特有样式
		lines: $i;
		text-overflow: ellipsis;
		overflow: hidden;
		flex: 1;
		/* #endif */

		/* #ifndef APP-NVUE */
		// vue下，单行和多行显示省略号需要单独处理
		@if $i == '1' {
			overflow: hidden;
			white-space: nowrap;
			text-overflow: ellipsis;
		} @else {
			display: -webkit-box!important;
			overflow: hidden;
			text-overflow: ellipsis;
			word-break: break-all;
			-webkit-line-clamp: $i;
			-webkit-box-orient: vertical!important;
		}
		/* #endif */
	}
}
$uv-bordercolor: #dadbde;
@if variable-exists(uv-border-color) {
	$uv-bordercolor: $uv-border-color;
}

// 此处加上!important并非随意乱用，而是因为目前*.nvue页面编译到H5时，
// App.vue的样式会被uni-app的view元素的自带border属性覆盖，导致无效
// 综上，这是uni-app的缺陷导致我们为了多端兼容，而必须要加上!important
// 移动端兼容性较好，直接使用0.5px去实现细边框，不使用伪元素形式实现
.uv-border {
	border-width: 0.5px!important;
	border-color: $uv-bordercolor!important;
    border-style: solid;
}

.uv-border-top {
	border-top-width: 0.5px!important;
	border-color: $uv-bordercolor!important;
    border-top-style: solid;
}

.uv-border-left {
	border-left-width: 0.5px!important;
	border-color: $uv-bordercolor!important;
    border-left-style: solid;
}

.uv-border-right {
	border-right-width: 0.5px!important;
	border-color: $uv-bordercolor!important;
    border-right-style: solid;
}

.uv-border-bottom {
	border-bottom-width: 0.5px!important;
	border-color: $uv-bordercolor!important;
    border-bottom-style: solid;
}

.uv-border-top-bottom {
	border-top-width: 0.5px!important;
	border-bottom-width: 0.5px!important;
	border-color: $uv-bordercolor!important;
    border-top-style: solid;
    border-bottom-style: solid;
}

// 去除button的所有默认样式，让其表现跟普通的view、text元素一样
.uv-reset-button {
	padding: 0;
	background-color: transparent;
	/* #ifndef APP-PLUS */
	font-size: inherit;
	line-height: inherit;
	color: inherit;
	/* #endif */
	/* #ifdef APP-NVUE */
	border-width: 0;
	/* #endif */
}

/* #ifndef APP-NVUE */
.uv-reset-button::after {
   border: none;
}
/* #endif */

.uv-hover-class {
	opacity: 0.7;
}

