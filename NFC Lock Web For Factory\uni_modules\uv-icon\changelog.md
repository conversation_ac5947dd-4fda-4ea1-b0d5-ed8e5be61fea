## 1.0.13（2023-12-06）
1. 优化
## 1.0.12（2023-12-06）
1. 阻止事件冒泡处理
## 1.0.11（2023-10-29）
1. imgMode默认值改成aspectFit
## 1.0.10（2023-08-13）
1. 优化nvue，方便自定义图标
## 1.0.9（2023-07-28）
1. 修改几个对应错误图标的BUG
## 1.0.8（2023-07-24）
1. 优化 支持base64图片
## 1.0.7（2023-07-17）
1. 修复  uv-icon 恢复uv-empty相关的图标
## 1.0.6（2023-07-13）
1. 修复icon设置name属性对应图标错误的BUG
## 1.0.5（2023-07-04）
1. 更新图标，删除一些不常用的图标
2. 删除base64，修改成ttf文件引入读取图标
3. 自定义图标文档说明：https://www.uvui.cn/guide/customIcon.html
## 1.0.4（2023-07-03）
1. 修复主题颜色在APP不生效的BUG
## 1.0.3（2023-05-24）
1. 将线上ttf字体包替换成base64，避免加载时或者网络差时候显示白色方块
## 1.0.2（2023-05-16）
1. 优化组件依赖，修改后无需全局引入，组件导入即可使用
2. 优化部分功能
## 1.0.1（2023-05-10）
1. 修复小程序中异常显示
## 1.0.0（2023-05-04）
新发版
